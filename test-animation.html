<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>动画效果测试</title>
    <style>
      body {
        background: #000;
        color: #fff;
        font-family: Arial, sans-serif;
        padding: 50px;
      }

      .test-container {
        width: 365px;
        height: 50px;
        background: rgba(50, 254, 252, 0.1);
        border: 1px solid rgba(50, 254, 252, 0.3);
        margin: 20px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }

      /* 缩放动画效果 - 从小到大 */
      .scale-animation {
        animation: scaleUpAnimation 600ms cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
        animation-delay: 0.4s; /* 在滑动动画完成后开始 */
        transform: scale(0.3);
        opacity: 1;
        transform-origin: center center;
      }

      .scale-animation-2 {
        animation-delay: 0.6s; /* 第二个容器延迟更久 */
      }

      @keyframes scaleUpAnimation {
        0% {
          transform: scale(0.3);
          filter: blur(2px) brightness(0.8);
        }
        30% {
          transform: scale(0.6);
          filter: blur(1px) brightness(0.9);
        }
        70% {
          transform: scale(1.1);
          filter: blur(0px) brightness(1.1);
        }
        100% {
          transform: scale(1);
          filter: blur(0px) brightness(1);
        }
      }

      /* 增强3D翻转动画效果 */
      .enhanced-3d-flip {
        perspective: 1000px;
        transform-style: preserve-3d;
        animation: enhanced3DFlip 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
        transform: rotateY(-90deg) scale(0.8);
        opacity: 0;
        filter: drop-shadow(0 10px 20px rgba(50, 254, 252, 0.3));
        transition: all 0.3s ease;
        width: 50%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(50, 254, 252, 0.05);
        margin: 0 5px;
      }

      .enhanced-3d-flip:hover {
        transform: rotateY(0deg) scale(1.05);
        filter: drop-shadow(0 15px 30px rgba(50, 254, 252, 0.5)) brightness(1.1);
      }

      @keyframes enhanced3DFlip {
        0% {
          transform: rotateY(-90deg) scale(0.8);
          opacity: 0;
          filter: drop-shadow(0 5px 10px rgba(50, 254, 252, 0.2));
        }
        50% {
          transform: rotateY(-45deg) scale(0.9);
          opacity: 0.7;
          filter: drop-shadow(0 8px 16px rgba(50, 254, 252, 0.4));
        }
        100% {
          transform: rotateY(0deg) scale(1);
          opacity: 1;
          filter: drop-shadow(0 10px 20px rgba(50, 254, 252, 0.3));
        }
      }

      .btn {
        background: rgba(50, 254, 252, 0.2);
        border: 1px solid rgba(50, 254, 252, 0.5);
        color: #fff;
        padding: 10px 20px;
        cursor: pointer;
        margin: 10px;
      }

      .btn:hover {
        background: rgba(50, 254, 252, 0.3);
      }
    </style>
  </head>
  <body>
    <h1>LeftHead1Body1_1 动画效果测试</h1>

    <div class="test-container scale-animation" id="container1">
      <div class="enhanced-3d-flip" style="animation-delay: 0.5s">总警告数: 92.94%</div>
      <div class="enhanced-3d-flip" style="animation-delay: 0.6s">自动拦截攻击: 1234</div>
    </div>

    <div class="test-container scale-animation scale-animation-2" id="container2">
      <div class="enhanced-3d-flip" style="animation-delay: 0.7s">自动拦截率: 85%</div>
      <div class="enhanced-3d-flip" style="animation-delay: 0.8s">告警处置率: 100%</div>
    </div>

    <button class="btn" onclick="restartAnimation()">重新播放动画</button>

    <script>
      function restartAnimation() {
        const containers = document.querySelectorAll(".scale-animation");
        const flips = document.querySelectorAll(".enhanced-3d-flip");

        // 移除动画类
        containers.forEach((container) => {
          container.style.animation = "none";
        });
        flips.forEach((flip) => {
          flip.style.animation = "none";
        });

        // 强制重排
        document.body.offsetHeight;

        // 重新添加动画类
        setTimeout(() => {
          containers.forEach((container, index) => {
            container.style.animation = `scaleUpAnimation 500ms cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards`;
            container.style.animationDelay = index * 0.2 + "s";
          });
          flips.forEach((flip, index) => {
            flip.style.animation = `enhanced3DFlip 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards`;
            flip.style.animationDelay = index * 0.1 + "s";
          });
        }, 50);
      }
    </script>
  </body>
</html>
