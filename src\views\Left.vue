<template>
  <div class="Left" :style="animationStyles">
    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1" :style="{ animationDelay: animationDelays[0] }">
        <div class="LeftHead1Text">网络攻击分析</div>
      </div>
    </transition>
    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1Body1 scale-animation" :style="{ animationDelay: animationDelays[1] }">
        <div class="LeftHead1Body1_1 enhanced-3d-flip" :style="{ animationDelay: animationDelays[1] }">
          <Label1 :label1Right2Text="dataStore.data?.intercept?.countAttackNum" />
        </div>
        <div class="LeftHead1Body1_1 LeftHead1Body1_2 enhanced-3d-flip" :style="{ animationDelay: `${parseFloat(animationDelays[1]) + 0.1}s` }">
          <Label1 :iconId="2" label1Right1Text="自动拦截攻击" :label1Right2Text="dataStore.data?.intercept?.autIntercept" />
        </div>
      </div>
    </transition>
    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1Body1 LeftHead1Body12 scale-animation" :style="{ animationDelay: animationDelays[2] }">
        <div class="LeftHead1Body1_1 enhanced-3d-flip" :style="{ animationDelay: animationDelays[2] }">
          <Label1 :iconId="3" label1Right1Text="自动拦截率" :label1Right2Text="dataStore.data?.intercept?.autoRatio" />
        </div>
        <div class="LeftHead1Body1_1 LeftHead1Body1_2 enhanced-3d-flip" :style="{ animationDelay: `${parseFloat(animationDelays[2]) + 0.1}s` }">
          <Label1 :iconId="4" label1Right1Text="告警处置率" label1Right2Text="100%" />
        </div>
      </div>
    </transition>

    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1 LeftHead2" :style="{ animationDelay: animationDelays[3] }">
        <div class="LeftHead1Text">攻击风险统计</div>
      </div>
    </transition>
    <!-- <div class="LeftHead2Body"></div> -->
    <div v-if="uiAnimationTriggered" class="LeftHead2Body" :style="{ animationDelay: animationDelays[4] }">
      <Bt1 :enableLoadingAnimation="true" :animationDelay="animationDelays[4]" />
    </div>

    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1 LeftHead3" :style="{ animationDelay: animationDelays[5] }">
        <div class="LeftHead1Text">主要攻击来源</div>
      </div>
    </transition>
    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead3Body" :style="{ animationDelay: animationDelays[6] }"><LeftList1 /></div>
    </transition>

    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1 LeftHead4" :style="{ animationDelay: animationDelays[7] }">
        <div class="LeftHead1Text">政击告警风险趋势</div>
      </div>
    </transition>
    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead4Body" :style="{ animationDelay: animationDelays[8] }">
        <Qx1 :enableLoadingAnimation="true" :animationDelay="animationDelays[8]" />
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, inject } from "vue";
import Label1 from "./Label1.vue";
import Bt1 from "./Bt1.vue";
import LeftList1 from "./LeftList1.vue";
import Qx1 from "./Qx1.vue";

import { usedata } from "../store/data";
const dataStore = usedata();

// 注入UI动画触发状态
const uiAnimationTriggered = inject("uiAnimationTriggered", ref(false));

// 动画总时长配置 (秒)
const totalAnimationDuration = ref(1);

// 计算单个动画持续时间 (总时长的1/3)
const singleAnimationDuration = computed(() => {
  return totalAnimationDuration.value / 3;
});

// 计算动画延迟间隔 (总时长除以元素数量)
const delayInterval = computed(() => {
  return totalAnimationDuration.value / 9; // 9个元素
});

// 计算各个元素的动画延迟
const animationDelays = computed(() => {
  const delays = [];
  for (let i = 0; i < 9; i++) {
    delays.push((i * delayInterval.value).toFixed(2) + "s");
  }
  return delays;
});

// CSS 变量，用于动态设置动画时长
const animationStyles = computed(() => {
  return {
    "--animation-duration": singleAnimationDuration.value + "s",
    "--total-duration": totalAnimationDuration.value + "s",
  };
});
</script>

<style lang="less" scoped>
/* 滑动动画样式 */
.slide-left-enter-active {
  animation: slideInFromLeft var(--animation-duration, 1s) ease-out forwards;
}

.slide-left-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 为每个元素添加初始状态和动画延迟 */
.LeftHead1,
.LeftHead1Body1,
.LeftHead1Body12,
.LeftHead2,
.LeftHead3,
.LeftHead3Body,
.LeftHead4,
.LeftHead4Body {
  animation: slideInFromLeft var(--animation-duration, 1s) ease-out forwards;
  transform: translateX(-100%);
  opacity: 0;
}

/* LeftHead2Body 不使用滑动动画，直接显示 */
.LeftHead2Body {
  opacity: 1;
  transform: translateX(0);
}

/* 增强3D翻转动画效果 */
.enhanced-3d-flip {
  perspective: 1000px;
  transform-style: preserve-3d;
  animation: enhanced3DFlip 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  transform: rotateY(-90deg) scale(0.8);
  opacity: 0;
  filter: drop-shadow(0 10px 20px rgba(50, 254, 252, 0.3));
  transition: all 0.3s ease;
}

.enhanced-3d-flip:hover {
  transform: rotateY(0deg) scale(1.05);
  filter: drop-shadow(0 15px 30px rgba(50, 254, 252, 0.5)) brightness(1.1);
}

@keyframes enhanced3DFlip {
  0% {
    transform: rotateY(-90deg) scale(0.8);
    opacity: 0;
    filter: drop-shadow(0 5px 10px rgba(50, 254, 252, 0.2));
  }
  50% {
    transform: rotateY(-45deg) scale(0.9);
    opacity: 0.7;
    filter: drop-shadow(0 8px 16px rgba(50, 254, 252, 0.4));
  }
  100% {
    transform: rotateY(0deg) scale(1);
    opacity: 1;
    filter: drop-shadow(0 10px 20px rgba(50, 254, 252, 0.3));
  }
}

/* 缩放动画效果 - 从小到大 */
.scale-animation {
  animation: scaleUpAnimation 500ms cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  transform: scale(0.3);
  opacity: 0;
  transform-origin: center center;
}

@keyframes scaleUpAnimation {
  0% {
    transform: scale(0.3);
    opacity: 0;
    filter: blur(2px);
  }
  30% {
    transform: scale(0.6);
    opacity: 0.5;
    filter: blur(1px);
  }
  70% {
    transform: scale(1.05);
    opacity: 0.9;
    filter: blur(0px);
  }
  100% {
    transform: scale(1);
    opacity: 1;
    filter: blur(0px);
  }
}

.Left {
  width: 100%;
  height: 100%;
  position: relative;
  .LeftHead1 {
    position: absolute;
    left: 147px;
    top: 15px;
    width: 380px;
    height: 35px;
    background-image: url("../../public/img/cardHead.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .LeftHead1Text {
      font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
      font-weight: bold;
      font-size: 18px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
      margin-left: 25px;
    }
  }
  .LeftHead2 {
    position: absolute;
    left: 91.0696px;
    top: 200px;
  }
  .LeftHead3 {
    position: absolute;
    left: 77px;
    top: 423px;
  }
  .LeftHead4 {
    position: absolute;
    left: 92.25px;
    top: 659px;
  }
  .LeftHead1Body1 {
    position: absolute;
    left: 125px;
    top: 71px;
    width: 365px;
    height: 50px;

    display: flex;
    justify-content: center;
    align-items: center;

    .LeftHead1Body1_1 {
      width: 50%;
      height: 100%;
    }
    .LeftHead1Body1_2 {
      width: 50%;
      height: 100%;
    }
  }
  .LeftHead1Body12 {
    position: absolute;
    left: 106px;
    top: 130px;
  }
  .LeftHead2Body {
    position: absolute;
    left: 89.7361px;
    top: 253px;
    width: 370px;
    height: 150px;
  }
  .LeftHead3Body {
    position: absolute;
    left: 85px;
    top: 455px;
    width: 370px;
    height: 170px;
  }
  .LeftHead4Body {
    position: absolute;
    left: 102px;
    top: 694px;
    width: 370px;
    height: 170px;
  }
}
</style>
